/* Size Chart Modal Styles */
.size-chart-modal .modal-dialog {
    max-width: 900px;
}

.unit-toggle .btn {
    border-radius: 20px;
    margin: 0 5px;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.unit-toggle .btn.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.unit-icon {
    width: 16px;
    height: 16px;
    margin-right: 5px;
}

.size-chart-table {
    font-size: 14px;
    margin-bottom: 0;
}

.size-chart-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 12px 8px;
}

.size-chart-table td {
    text-align: center;
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 10px 8px;
}

.size-chart-table .font-weight-bold {
    background-color: #f8f9fa;
    font-weight: 600;
}

.measurement-value {
    position: relative;
}

.cm-value, .inch-value {
    display: inline-block;
    transition: opacity 0.3s ease;
}

.size-guide-description {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}

/* Country Selection Modal Styles */
#countrySelectionModal .modal-dialog {
    max-width: 600px;
}

.country-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.country-option {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.country-option:hover {
    transform: translateY(-2px);
}

.country-card {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    background: white;
}

.country-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.country-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.country-flag {
    margin-bottom: 10px;
}

.flag-img {
    width: 40px;
    height: 30px;
    object-fit: cover;
    border-radius: 3px;
    border: 1px solid #ddd;
}

.flag-placeholder {
    width: 40px;
    height: 30px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.flag-placeholder i {
    color: #6c757d;
    font-size: 16px;
}

.country-name {
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.currency-info {
    color: #6c757d;
    font-weight: 500;
}

/* Current Country Display */
.current-country-display {
    margin-left: 15px;
}

.current-country-display .btn {
    font-size: 13px;
    padding: 5px 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .size-chart-modal .modal-dialog {
        max-width: 95%;
        margin: 10px auto;
    }
    
    .size-chart-table {
        font-size: 12px;
    }
    
    .size-chart-table th,
    .size-chart-table td {
        padding: 8px 4px;
    }
    
    .unit-toggle .btn {
        padding: 6px 12px;
        font-size: 13px;
    }
    
    .country-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }
    
    .country-card {
        padding: 15px;
    }
    
    #countrySelectionModal .modal-dialog {
        max-width: 95%;
        margin: 10px auto;
    }
}

@media (max-width: 576px) {
    .size-chart-table th,
    .size-chart-table td {
        padding: 6px 2px;
        font-size: 11px;
    }
    
    .country-grid {
        grid-template-columns: 1fr;
    }
    
    .unit-toggle {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    
    .unit-toggle .btn {
        margin: 0;
        width: 100%;
    }
}

/* Animation for modal transitions */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: none;
}

/* Loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error states */
.alert-success {
    border-color: #28a745;
    background-color: #d4edda;
    color: #155724;
}

.alert-error {
    border-color: #dc3545;
    background-color: #f8d7da;
    color: #721c24;
}

