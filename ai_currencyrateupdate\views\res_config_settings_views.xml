<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.currency.rate.update</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//app[1]" position="after">
                <app string="Currency Rate Update" name="ai_currencyrateupdate">
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <label for="openexchangerates_api_key" string="OpenExchangeRates API Key"/>
                                <div class="text-muted">
                                    Enter your OpenExchangeRates API key for automatic currency rate updates
                                </div>
                                <div class="mt8">
                                    <field name="openexchangerates_api_key" class="o_field_char"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </app>
            </xpath>
        </field>
    </record>

    <!-- Main Menu and Actions -->
    <menuitem id="menu_currency_rate_update_root"
        name="Currency Update"
        sequence="95"/>

    <!-- Settings Action -->
    <record id="action_currency_rate_update_configuration" model="ir.actions.act_window">
        <field name="name">Currency Rate Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
    </record>

    <!-- Settings Menu Item -->
    <menuitem id="menu_currency_rate_update_settings"
        name="Settings"
        parent="menu_currency_rate_update_root"
        action="action_currency_rate_update_configuration"
        sequence="10"/>

    <!-- Currency Rates Action -->
    <record id="action_currency_rates" model="ir.actions.act_window">
        <field name="name">Currency Rates</field>
        <field name="res_model">res.currency.rate</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No currency rates found!
            </p>
            <p>
                Currency rates will be automatically updated using OpenExchangeRates API.
            </p>
        </field>
    </record>

    <!-- Currency Rates Menu Item -->
    <menuitem id="menu_currency_rates"
        name="Currency Rates"
        parent="menu_currency_rate_update_root"
        action="action_currency_rates"
        sequence="10"/>

    <!-- Manual Update Action -->
    <record id="action_currency_rate_update" model="ir.actions.act_window">
        <field name="name">Update Rates</field>
        <field name="res_model">currency.rate.update</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Manual Update Menu Item -->
    <menuitem id="menu_currency_rate_update"
        name="Update Rates"
        parent="menu_currency_rate_update_root"
        action="action_currency_rate_update"
        sequence="20"/>
</odoo>